const { exec } = require("child_process");

const packageDir = __dirname;

async function runTopWorkplacesScript() {
  return new Promise((resolve, reject) => {
    exec("npm run start:topWorkplaces --silent", { cwd: packageDir }, (error, stdout, stderr) => {
      if (error) {
        reject(`<PERSON>rror executing command: ${error}`);
        return;
      }
      if (stderr) {
        reject(`Error executing script: ${stderr}`);
        return;
      }
      resolve(stdout.toString());
    });
  });
