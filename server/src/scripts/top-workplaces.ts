console.log("TODO: Implement me!");
import axios from "axios";
const getTopWorkplaces = async () => {
  try {
    const workplaces = (await axios.get("http://localhost:3000/workplaces")).data.data;
    let shifts: any[] = [];
    let done = false;
    let link = "http://localhost:3000/shifts";
    while (!done && link) {
      const { data, links } = (await axios.get(link)).data;
      shifts = [...shifts, ...data];
      done = !links.next;
      link = links.next;
    }
    const result: any[] = workplaces.reduce(
      (acc: { name: string; shifts: number }[], workplace: any) => {
        const workplaceShifts = shifts.filter((shift) => shift.workplaceId === workplace.id);
        acc.push({
          name: workplace.name,
          shifts: workplaceShifts.length,
        });
        return acc;
      },
      [],
    );
    const sortedResult = result.sort((a, b) => b.shifts - a.shifts).slice(0, 3);

    return sortedResult;
  } catch (error) {
    throw error;
  }