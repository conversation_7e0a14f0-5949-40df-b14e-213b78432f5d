interface Workplace {
  id: number;
  name: string;
  status: number;
}

interface Shift {
  id: number;
  createdAt: string;
  startAt: string;
  endAt: string;
  workplaceId: number;
  workerId: number | null;
  cancelledAt: string | null;
}

interface PaginatedResponse<T> {
  data: T[];
  links: { next?: string };
}

interface WorkplaceResult {
  name: string;
  shifts: number;
}

class HttpError extends Error {
  constructor(
    message: string,
    public status: number,
    public url: string,
  ) {
    super(message);
    this.name = "HttpError";
  }
}

class NetworkError extends Error {
  constructor(
    message: string,
    public url: string,
  ) {
    super(message);
    this.name = "NetworkError";
  }
}

const DEFAULT_TIMEOUT = 10000;
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;
const BASE_URL = "http://localhost:3000";

const sleep = (ms: number): Promise<void> => new Promise((resolve) => setTimeout(resolve, ms));

const validateUrl = (url: string): void => {
  if (!url || typeof url !== "string") {
    throw new Error("Invalid URL provided");
  }

  try {
    new URL(url);
  } catch {
    throw new Error(`Invalid URL format: ${url}`);
  }
};

const createAbortController = (timeoutMs: number): AbortController => {
  const controller = new AbortController();
  setTimeout(() => controller.abort(), timeoutMs);
  return controller;
};

const fetchWithRetry = async <T>(
  url: string,
  options: RequestInit = {},
  retries = MAX_RETRIES,
  timeout = DEFAULT_TIMEOUT,
): Promise<T> => {
  validateUrl(url);

  for (let attempt = 1; attempt <= retries; attempt++) {
    const controller = createAbortController(timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          ...options.headers,
        },
      });

      if (!response.ok) {
        throw new HttpError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          url,
        );
      }

      const contentType = response.headers.get("content-type");
      if (!contentType?.includes("application/json")) {
        throw new Error(`Expected JSON response, got: ${contentType}`);
      }

      const data = await response.json();

      if (!data || typeof data !== "object") {
        throw new Error("Invalid JSON response structure");
      }

      return data as T;
    } catch (error) {
      const isLastAttempt = attempt === retries;

      if (error instanceof HttpError) {
        if (error.status >= 400 && error.status < 500) {
          throw error;
        }
        if (isLastAttempt) throw error;
      } else if (error instanceof Error) {
        if (error.name === "AbortError") {
          const timeoutError = new NetworkError(`Request timeout after ${timeout}ms`, url);
          if (isLastAttempt) throw timeoutError;
        } else if (error.name === "TypeError" && error.message.includes("fetch")) {
          const networkError = new NetworkError(`Network error: ${error.message}`, url);
          if (isLastAttempt) throw networkError;
        } else {
          if (isLastAttempt) throw error;
        }
      } else {
        if (isLastAttempt) throw new Error(`Unknown error: ${String(error)}`);
      }

      if (!isLastAttempt) {
        await sleep(RETRY_DELAY * attempt);
      }
    }
  }

  throw new Error("Max retries exceeded");
};

const validateWorkplacesResponse = (data: unknown): Workplace[] => {
  if (!data || typeof data !== "object" || !("data" in data)) {
    throw new Error("Invalid workplaces response: missing data property");
  }

  const response = data as { data: unknown };

  if (!Array.isArray(response.data)) {
    throw new Error("Invalid workplaces response: data is not an array");
  }

  return response.data.map((workplace: unknown, index: number) => {
    if (!workplace || typeof workplace !== "object") {
      throw new Error(`Invalid workplace at index ${index}: not an object`);
    }

    const wp = workplace as Record<string, unknown>;

    if (typeof wp.id !== "number" || wp.id <= 0) {
      throw new Error(`Invalid workplace at index ${index}: invalid id`);
    }

    if (typeof wp.name !== "string" || wp.name.trim().length === 0) {
      throw new Error(`Invalid workplace at index ${index}: invalid name`);
    }

    if (typeof wp.status !== "number") {
      throw new Error(`Invalid workplace at index ${index}: invalid status`);
    }

    return {
      id: wp.id,
      name: wp.name.trim(),
      status: wp.status,
    };
  });
};

const validateShiftsResponse = (data: unknown): PaginatedResponse<Shift> => {
  if (!data || typeof data !== "object") {
    throw new Error("Invalid shifts response: not an object");
  }

  const response = data as Record<string, unknown>;

  if (!("data" in response) || !Array.isArray(response.data)) {
    throw new Error("Invalid shifts response: missing or invalid data array");
  }

  if (!("links" in response) || !response.links || typeof response.links !== "object") {
    throw new Error("Invalid shifts response: missing or invalid links object");
  }

  const links = response.links as Record<string, unknown>;

  if (links.next !== undefined && typeof links.next !== "string") {
    throw new Error("Invalid shifts response: next link must be string or undefined");
  }

  const shifts = response.data.map((shift: unknown, index: number) => {
    if (!shift || typeof shift !== "object") {
      throw new Error(`Invalid shift at index ${index}: not an object`);
    }

    const s = shift as Record<string, unknown>;

    if (typeof s.id !== "number" || s.id <= 0) {
      throw new Error(`Invalid shift at index ${index}: invalid id`);
    }

    if (typeof s.workplaceId !== "number" || s.workplaceId <= 0) {
      throw new Error(`Invalid shift at index ${index}: invalid workplaceId`);
    }

    const requiredStringFields = ["createdAt", "startAt", "endAt"];
    for (const field of requiredStringFields) {
      if (typeof s[field] !== "string" || (s[field] as string).trim().length === 0) {
        throw new Error(`Invalid shift at index ${index}: invalid ${field}`);
      }
    }

    if (s.workerId !== null && typeof s.workerId !== "number") {
      throw new Error(`Invalid shift at index ${index}: workerId must be number or null`);
    }

    if (s.cancelledAt !== null && typeof s.cancelledAt !== "string") {
      throw new Error(`Invalid shift at index ${index}: cancelledAt must be string or null`);
    }

    return {
      id: s.id,
      createdAt: (s.createdAt as string).trim(),
      startAt: (s.startAt as string).trim(),
      endAt: (s.endAt as string).trim(),
      workplaceId: s.workplaceId,
      workerId: s.workerId as number | null,
      cancelledAt: s.cancelledAt as string | null,
    };
  });

  return {
    data: shifts,
    links: { next: links.next as string | undefined },
  };
};

const fetchWorkplaces = async (): Promise<Workplace[]> => {
  const url = `${BASE_URL}/workplaces`;
  const response = await fetchWithRetry<unknown>(url);
  return validateWorkplacesResponse(response);
};

const fetchAllShifts = async (): Promise<Shift[]> => {
  const shifts: Shift[] = [];
  let nextUrl: string | undefined = `${BASE_URL}/shifts`;

  while (nextUrl) {
    const response = await fetchWithRetry<unknown>(nextUrl);
    const validatedResponse = validateShiftsResponse(response);

    shifts.push(...validatedResponse.data);
    nextUrl = validatedResponse.links.next;

    if (nextUrl && !nextUrl.startsWith("http")) {
      nextUrl = nextUrl.startsWith("/") ? `${BASE_URL}${nextUrl}` : `${BASE_URL}/${nextUrl}`;
    }
  }

  return shifts;
};

const calculateWorkplaceShifts = (workplaces: Workplace[], shifts: Shift[]): WorkplaceResult[] => {
  if (!Array.isArray(workplaces) || !Array.isArray(shifts)) {
    throw new Error("Invalid input: workplaces and shifts must be arrays");
  }

  const shiftCountMap = new Map<number, number>();

  for (const shift of shifts) {
    const count = shiftCountMap.get(shift.workplaceId) || 0;
    shiftCountMap.set(shift.workplaceId, count + 1);
  }

  return workplaces.map((workplace) => ({
    name: workplace.name,
    shifts: shiftCountMap.get(workplace.id) || 0,
  }));
};

const getTopWorkplaces = async (): Promise<WorkplaceResult[]> => {
  try {
    const [workplaces, shifts] = await Promise.all([fetchWorkplaces(), fetchAllShifts()]);

    if (workplaces.length === 0) {
      return [];
    }

    const workplaceResults = calculateWorkplaceShifts(workplaces, shifts);

    return workplaceResults.sort((a, b) => b.shifts - a.shifts).slice(0, 3);
  } catch (error) {
    if (error instanceof HttpError) {
      throw new Error(`HTTP request failed: ${error.message} (${error.url})`);
    } else if (error instanceof NetworkError) {
      throw new Error(`Network request failed: ${error.message} (${error.url})`);
    } else if (error instanceof Error) {
      throw new Error(`Failed to fetch top workplaces: ${error.message}`);
    } else {
      throw new Error(`Unknown error occurred: ${String(error)}`);
    }
  }
};

export default getTopWorkplaces;
